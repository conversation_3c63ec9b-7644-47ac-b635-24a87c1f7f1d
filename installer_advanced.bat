@echo off
title Installation Assistant de Visee Avance

echo ========================================
echo    Assistant de Visee Avance v2.0
echo    Installation des composants
echo ========================================
echo.

echo Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe
    echo Telechargez depuis: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python detecte!
echo.

echo Installation des dependances avancees...
pip install --upgrade pip
pip install -r requirements_advanced.txt

if errorlevel 1 (
    echo.
    echo ERREUR lors de l'installation
    echo Tentative d'installation manuelle...
    pip install pyautogui numpy pillow pywin32 psutil opencv-python
)

echo.
echo Verification des permissions...
echo Certaines fonctions necessitent des droits administrateur
echo.

echo ========================================
echo    Installation terminee!
echo ========================================
echo.
echo IMPORTANT:
echo - Lancez en tant qu'administrateur pour toutes les fonctions
echo - Utilisez de maniere responsable uniquement
echo - Respectez les conditions d'utilisation des jeux
echo.
echo Pour lancer: double-clic sur lancer_advanced.bat
echo.
pause
