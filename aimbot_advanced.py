#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aimbot Professionnel - Version complète avec toutes les fonctionnalités
Version: 3.0 - Interface professionnelle complète
"""

import tkinter as tk
from tkinter import ttk, colorchooser, messagebox
import numpy as np
from PIL import Image, ImageTk, ImageDraw
import threading
import time
import json
import os
import random
import ctypes
from ctypes import wintypes
import win32api
import win32con
import win32gui
import win32process
import psutil
import math

class AimbotProfessional:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("88Software Pixel Bot 10.0.0 (Profile 1)")
        self.root.geometry("800x700")
        self.root.resizable(False, False)
        self.root.configure(bg='#1e1e1e')

        # Variables de configuration complètes
        self.target_color = (128, 0, 128)  # Purple par défaut
        self.color_name = "Purple"

        # Boutons configurables
        self.button1_action = "Left Click"
        self.button2_action = "None"

        # FOV (Field of View)
        self.fov_x = 95
        self.fov_y = 75

        # Lissage séparé X/Y
        self.smooth_x = 10.9
        self.smooth_y = 14

        # Vitesse
        self.speed = 1

        # Offset
        self.offset_x = 1
        self.offset_y = 7

        # Mode X uniquement
        self.only_x_axis = False

        # États
        self.active = False
        self.running = False

        # Anti-détection
        self.stealth_mode = True

        # Configuration
        self.config_file = "pixelbot_config.json"
        self.load_config()

        # Interface moderne
        self.create_professional_interface()

        # Système de détection
        self.detection_thread = None
        self.last_move_time = 0

        # Initialisation
        self.init_system_hooks()
        
    def init_system_hooks(self):
        """Initialise les hooks système pour l'anti-détection"""
        try:
            # Masquage du processus
            self.hide_from_process_list()
            
            # Hook clavier pour arrêt d'urgence discret
            self.setup_emergency_stop()
            
        except Exception as e:
            print(f"Avertissement hooks: {e}")
    
    def hide_from_process_list(self):
        """Techniques pour masquer le processus"""
        try:
            # Renommage du processus
            ctypes.windll.kernel32.SetConsoleTitleW("Windows Update Assistant")
            
            # Modification de la priorité pour être moins visible
            process = psutil.Process()
            process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            
        except Exception as e:
            pass
    
    def setup_emergency_stop(self):
        """Configure l'arrêt d'urgence discret"""
        # Combinaison de touches discrète: Ctrl+Shift+F12
        self.emergency_keys = [win32con.VK_CONTROL, win32con.VK_SHIFT, win32con.VK_F12]
    
    def create_professional_interface(self):
        """Interface professionnelle complète comme 88Software"""

        # Style sombre moderne
        self.setup_dark_theme()

        # Sidebar navigation
        self.create_sidebar()

        # Main content area
        self.main_frame = tk.Frame(self.root, bg='#2d2d2d')
        self.main_frame.pack(side="right", fill="both", expand=True)

        # Header
        self.create_header()

        # Aimbot content
        self.create_aimbot_content()

    def setup_dark_theme(self):
        """Configure le thème sombre professionnel"""
        style = ttk.Style()

        # Configuration du thème sombre
        style.theme_use('clam')

        # Couleurs personnalisées
        style.configure('Dark.TFrame', background='#2d2d2d')
        style.configure('Dark.TLabel', background='#2d2d2d', foreground='#ffffff')
        style.configure('Dark.TButton', background='#404040', foreground='#ffffff')
        style.configure('Dark.TCombobox', background='#404040', foreground='#ffffff')

    def create_sidebar(self):
        """Crée la sidebar de navigation"""
        sidebar = tk.Frame(self.root, bg='#1e1e1e', width=60)
        sidebar.pack(side="left", fill="y")
        sidebar.pack_propagate(False)

        # Icônes de navigation (simulées avec des labels)
        icons = ['🎯', '⚙️', '🔧', '⏱️', '🔗', '❓', '📊', '👥', '⚙️', 'ℹ️']

        for i, icon in enumerate(icons):
            btn = tk.Label(sidebar, text=icon, bg='#1e1e1e', fg='#666666',
                          font=('Arial', 16), cursor='hand2')
            btn.pack(pady=10)

            # Highlight pour l'aimbot (premier bouton)
            if i == 0:
                btn.configure(fg='#4a9eff')

    def create_header(self):
        """Crée l'en-tête avec titre et description"""
        header_frame = tk.Frame(self.main_frame, bg='#2d2d2d')
        header_frame.pack(fill="x", padx=20, pady=20)

        # Titre
        title_label = tk.Label(header_frame, text="Aimbot",
                              bg='#2d2d2d', fg='#ffffff',
                              font=('Arial', 24, 'bold'))
        title_label.pack(anchor="w")

        # Description
        desc_label = tk.Label(header_frame,
                             text="Pixel aimbot detects specific pixel colors on the screen and moves the aim to those targets automatically.",
                             bg='#2d2d2d', fg='#888888',
                             font=('Arial', 10), wraplength=600)
        desc_label.pack(anchor="w", pady=(5, 0))
        
    def create_aimbot_content(self):
        """Crée le contenu principal de l'aimbot"""
        content_frame = tk.Frame(self.main_frame, bg='#2d2d2d')
        content_frame.pack(fill="both", expand=True, padx=20)

        # Première ligne - Boutons et couleur
        row1 = tk.Frame(content_frame, bg='#2d2d2d')
        row1.pack(fill="x", pady=10)

        # Button 1
        self.create_dropdown_control(row1, "Button 1", self.button1_action,
                                   ["Left Click", "Right Click", "Middle Click", "None"],
                                   self.update_button1, 0)

        # Button 2
        self.create_dropdown_control(row1, "Button 2", self.button2_action,
                                   ["None", "Left Click", "Right Click", "Middle Click"],
                                   self.update_button2, 1)

        # Enemy Highlight Color
        self.create_color_control(row1, "Enemy Highlight Color", self.color_name, 2)

        # Deuxième ligne - FOV
        row2 = tk.Frame(content_frame, bg='#2d2d2d')
        row2.pack(fill="x", pady=10)

        # F.O.V X
        self.create_slider_control(row2, "F.O.V. X", self.fov_x, 1, 200,
                                 self.update_fov_x, 0)

        # F.O.V Y
        self.create_slider_control(row2, "F.O.V. Y", self.fov_y, 1, 200,
                                 self.update_fov_y, 1)

        # Only Move X Axis Mode
        self.create_dropdown_control(row2, "Only Move X Axis Mode",
                                   "Disable" if not self.only_x_axis else "Enable",
                                   ["Disable", "Enable"], self.update_x_axis_mode, 2)

        # Troisième ligne - Smooth
        row3 = tk.Frame(content_frame, bg='#2d2d2d')
        row3.pack(fill="x", pady=10)

        # Smooth X
        self.create_slider_control(row3, "Smooth X", self.smooth_x, 0.1, 50,
                                 self.update_smooth_x, 0, is_float=True)

        # Smooth Y
        self.create_slider_control(row3, "Smooth Y", self.smooth_y, 0.1, 50,
                                 self.update_smooth_y, 1, is_float=True)

        # Speed
        self.create_slider_control(row3, "Speed", self.speed, 0.1, 10,
                                 self.update_speed, 2, is_float=True)

        # Quatrième ligne - Offset
        row4 = tk.Frame(content_frame, bg='#2d2d2d')
        row4.pack(fill="x", pady=10)

        # Offset X
        self.create_slider_control(row4, "Offset X", self.offset_x, -50, 50,
                                 self.update_offset_x, 0)

        # Offset Y
        self.create_slider_control(row4, "Offset Y", self.offset_y, -50, 50,
                                 self.update_offset_y, 1)

        # Bouton de contrôle principal
        control_frame = tk.Frame(content_frame, bg='#2d2d2d')
        control_frame.pack(pady=30)

        self.toggle_btn = tk.Button(control_frame, text="START AIMBOT",
                                   font=('Arial', 12, 'bold'),
                                   bg='#4a9eff', fg='white',
                                   width=20, height=2,
                                   command=self.toggle_aimbot)
        self.toggle_btn.pack()

        # Status
        self.status_label = tk.Label(content_frame, text="Status: Inactive",
                                    bg='#2d2d2d', fg='#888888',
                                    font=('Arial', 10))
        self.status_label.pack(pady=10)
        
    def create_dropdown_control(self, parent, label, current_value, options, callback, column):
        """Crée un contrôle dropdown professionnel"""
        frame = tk.Frame(parent, bg='#2d2d2d')
        frame.grid(row=0, column=column, padx=20, pady=10, sticky="ew")
        parent.grid_columnconfigure(column, weight=1)

        # Label
        label_widget = tk.Label(frame, text=label, bg='#2d2d2d', fg='#ffffff',
                               font=('Arial', 10))
        label_widget.pack(anchor="w")

        # Dropdown avec style moderne
        dropdown_frame = tk.Frame(frame, bg='#404040', relief='solid', bd=1)
        dropdown_frame.pack(fill="x", pady=(5, 0))

        self.dropdown_var = tk.StringVar(value=current_value)
        dropdown = ttk.Combobox(dropdown_frame, textvariable=self.dropdown_var,
                               values=options, state="readonly",
                               font=('Arial', 9))
        dropdown.pack(fill="x", padx=2, pady=2)
        dropdown.bind('<<ComboboxSelected>>', lambda e: callback(self.dropdown_var.get()))

        # Stockage de la référence
        if not hasattr(self, 'dropdowns'):
            self.dropdowns = {}
        self.dropdowns[label] = dropdown

    def create_color_control(self, parent, label, current_color, column):
        """Crée un contrôle de couleur professionnel"""
        frame = tk.Frame(parent, bg='#2d2d2d')
        frame.grid(row=0, column=column, padx=20, pady=10, sticky="ew")
        parent.grid_columnconfigure(column, weight=1)

        # Label
        label_widget = tk.Label(frame, text=label, bg='#2d2d2d', fg='#ffffff',
                               font=('Arial', 10))
        label_widget.pack(anchor="w")

        # Color selector avec style moderne
        color_frame = tk.Frame(frame, bg='#404040', relief='solid', bd=1)
        color_frame.pack(fill="x", pady=(5, 0))

        # Indicateur de couleur
        self.color_indicator = tk.Label(color_frame, text="●",
                                       fg=f"#{self.target_color[0]:02x}{self.target_color[1]:02x}{self.target_color[2]:02x}",
                                       bg='#404040', font=('Arial', 16))
        self.color_indicator.pack(side="left", padx=5)

        # Dropdown de couleurs prédéfinies
        colors = ["Purple", "Red", "Green", "Blue", "Yellow", "Orange", "Pink", "Custom"]
        self.color_var = tk.StringVar(value=current_color)
        color_dropdown = ttk.Combobox(color_frame, textvariable=self.color_var,
                                     values=colors, state="readonly",
                                     font=('Arial', 9))
        color_dropdown.pack(side="left", fill="x", expand=True, padx=2, pady=2)
        color_dropdown.bind('<<ComboboxSelected>>', self.update_color)

    def create_slider_control(self, parent, label, current_value, min_val, max_val,
                            callback, column, is_float=False):
        """Crée un contrôle slider professionnel"""
        frame = tk.Frame(parent, bg='#2d2d2d')
        frame.grid(row=0, column=column, padx=20, pady=10, sticky="ew")
        parent.grid_columnconfigure(column, weight=1)

        # Label avec valeur
        label_frame = tk.Frame(frame, bg='#2d2d2d')
        label_frame.pack(fill="x")

        label_widget = tk.Label(label_frame, text=label, bg='#2d2d2d', fg='#ffffff',
                               font=('Arial', 10))
        label_widget.pack(side="left")

        # Valeur actuelle
        value_text = f"{current_value:.1f}" if is_float else str(int(current_value))
        value_label = tk.Label(label_frame, text=f"Value: {value_text}",
                              bg='#2d2d2d', fg='#888888', font=('Arial', 8))
        value_label.pack(side="right")

        # Slider avec style moderne
        slider_frame = tk.Frame(frame, bg='#2d2d2d')
        slider_frame.pack(fill="x", pady=(5, 0))

        # Variable pour le slider
        if is_float:
            slider_var = tk.DoubleVar(value=current_value)
        else:
            slider_var = tk.IntVar(value=int(current_value))

        # Slider personnalisé avec apparence moderne
        slider = tk.Scale(slider_frame, from_=min_val, to=max_val,
                         orient="horizontal", variable=slider_var,
                         bg='#404040', fg='#4a9eff',
                         highlightthickness=0, bd=0,
                         troughcolor='#2d2d2d', activebackground='#4a9eff',
                         font=('Arial', 8), showvalue=0,
                         command=lambda v: self.update_slider_value(callback, v, value_label, is_float))
        slider.pack(fill="x")

        # Stockage des références
        if not hasattr(self, 'sliders'):
            self.sliders = {}
        self.sliders[label] = {'slider': slider, 'value_label': value_label, 'var': slider_var}

    def update_slider_value(self, callback, value, value_label, is_float):
        """Met à jour la valeur affichée du slider"""
        if is_float:
            val = float(value)
            value_label.config(text=f"Value: {val:.1f}")
        else:
            val = int(float(value))
            value_label.config(text=f"Value: {val}")
        callback(val)
        
    # Fonctions de callback pour les contrôles
    def update_button1(self, value):
        """Met à jour l'action du bouton 1"""
        self.button1_action = value

    def update_button2(self, value):
        """Met à jour l'action du bouton 2"""
        self.button2_action = value

    def update_color(self, event=None):
        """Met à jour la couleur cible"""
        color_name = self.color_var.get()
        self.color_name = color_name

        # Couleurs prédéfinies
        color_map = {
            "Purple": (128, 0, 128),
            "Red": (255, 0, 0),
            "Green": (0, 255, 0),
            "Blue": (0, 0, 255),
            "Yellow": (255, 255, 0),
            "Orange": (255, 165, 0),
            "Pink": (255, 192, 203)
        }

        if color_name == "Custom":
            color = colorchooser.askcolor(title="Choisir une couleur personnalisée")
            if color[0]:
                self.target_color = tuple(map(int, color[0]))
                self.color_indicator.config(fg=color[1])
        elif color_name in color_map:
            self.target_color = color_map[color_name]
            color_hex = f"#{self.target_color[0]:02x}{self.target_color[1]:02x}{self.target_color[2]:02x}"
            self.color_indicator.config(fg=color_hex)

    def update_fov_x(self, value):
        """Met à jour FOV X"""
        self.fov_x = int(value)

    def update_fov_y(self, value):
        """Met à jour FOV Y"""
        self.fov_y = int(value)

    def update_x_axis_mode(self, value):
        """Met à jour le mode X uniquement"""
        self.only_x_axis = (value == "Enable")

    def update_smooth_x(self, value):
        """Met à jour le lissage X"""
        self.smooth_x = float(value)

    def update_smooth_y(self, value):
        """Met à jour le lissage Y"""
        self.smooth_y = float(value)

    def update_speed(self, value):
        """Met à jour la vitesse"""
        self.speed = float(value)

    def update_offset_x(self, value):
        """Met à jour l'offset X"""
        self.offset_x = int(value)

    def update_offset_y(self, value):
        """Met à jour l'offset Y"""
        self.offset_y = int(value)
        
    def toggle_aimbot(self):
        """Active/désactive l'aimbot"""
        if not self.active:
            self.start_aimbot()
        else:
            self.stop_aimbot()

    def start_aimbot(self):
        """Démarre l'aimbot"""
        self.active = True
        self.running = True
        self.toggle_btn.config(text="STOP AIMBOT", bg="#ff4444")
        self.status_label.config(text="Status: Active - Scanning for targets...", fg="#4a9eff")

        # Démarre le thread de détection
        self.detection_thread = threading.Thread(target=self.detection_loop, daemon=True)
        self.detection_thread.start()

    def stop_aimbot(self):
        """Arrête l'aimbot"""
        self.active = False
        self.running = False
        self.toggle_btn.config(text="START AIMBOT", bg="#4a9eff")
        self.status_label.config(text="Status: Inactive", fg="#888888")
        
    def detection_loop(self):
        """Boucle principale de détection avec toutes les fonctionnalités"""
        while self.running:
            try:
                if self.active:
                    target_pos = self.find_target_advanced()
                    if target_pos:
                        self.move_to_target_professional(target_pos)

                        # Exécute l'action du bouton si configurée
                        self.execute_button_action()

                time.sleep(0.001)  # Très rapide pour la réactivité

            except Exception as e:
                print(f"Erreur détection: {e}")
                time.sleep(0.01)

    def find_target_advanced(self):
        """Détection avancée avec FOV et algorithmes optimisés"""
        try:
            # Calcul de la région FOV
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)

            center_x = screen_width // 2
            center_y = screen_height // 2

            # Zone FOV basée sur les paramètres
            fov_width = int(self.fov_x * 4)  # Conversion en pixels
            fov_height = int(self.fov_y * 4)

            region = (
                center_x - fov_width // 2,
                center_y - fov_height // 2,
                fov_width,
                fov_height
            )

            # Capture d'écran optimisée
            screenshot = self.capture_screen_optimized(region)
            if screenshot is None:
                return None

            img_array = np.array(screenshot)

            # Détection de couleur avec tolérance adaptative
            target_pos = self.detect_color_advanced(img_array, region)

            if target_pos:
                # Application de l'offset
                adjusted_x = target_pos[0] + self.offset_x
                adjusted_y = target_pos[1] + self.offset_y
                return (adjusted_x, adjusted_y)

        except Exception as e:
            print(f"Erreur find_target: {e}")

        return None

    def capture_screen_optimized(self, region):
        """Capture d'écran optimisée pour les performances"""
        try:
            import pyautogui
            return pyautogui.screenshot(region=region)
        except Exception as e:
            return None

    def detect_color_advanced(self, img_array, region):
        """Détection de couleur avancée avec algorithmes optimisés"""
        target_r, target_g, target_b = self.target_color

        # Calcul de la distance de couleur
        diff = np.abs(img_array - [target_r, target_g, target_b])
        distance = np.sum(diff, axis=2)

        # Tolérance adaptative (basée sur la vitesse)
        tolerance = max(10, min(100, 30 + int(self.speed * 5)))

        # Masque de détection
        mask = distance <= tolerance * 3

        if np.any(mask):
            # Trouve le centre de masse des pixels correspondants
            y_coords, x_coords = np.where(mask)

            # Pondération par proximité du centre (plus proche = priorité)
            center_x = img_array.shape[1] // 2
            center_y = img_array.shape[0] // 2

            distances_to_center = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
            weights = 1.0 / (distances_to_center + 1)

            # Centre pondéré
            weighted_x = np.average(x_coords, weights=weights)
            weighted_y = np.average(y_coords, weights=weights)

            # Conversion en coordonnées écran
            screen_x = int(weighted_x) + region[0]
            screen_y = int(weighted_y) + region[1]

            return (screen_x, screen_y)

        return None
        
    def move_to_target_professional(self, target_pos):
        """Mouvement professionnel avec lissage avancé"""
        try:
            current_x, current_y = win32gui.GetCursorPos()
            target_x, target_y = target_pos

            # Mode X uniquement
            if self.only_x_axis:
                target_y = current_y

            # Calcul du mouvement avec lissage séparé X/Y
            diff_x = target_x - current_x
            diff_y = target_y - current_y

            # Distance totale
            distance = math.sqrt(diff_x**2 + diff_y**2)

            if distance < 2:  # Trop proche
                return

            # Application du lissage séparé
            smooth_factor_x = max(1, self.smooth_x)
            smooth_factor_y = max(1, self.smooth_y)

            # Calcul du mouvement avec vitesse
            move_x = (diff_x / smooth_factor_x) * self.speed
            move_y = (diff_y / smooth_factor_y) * self.speed

            # Limitation de la vitesse maximale
            max_move = 50
            move_x = max(-max_move, min(max_move, move_x))
            move_y = max(-max_move, min(max_move, move_y))

            # Mouvement final
            new_x = int(current_x + move_x)
            new_y = int(current_y + move_y)

            # Mouvement fluide avec interpolation
            self.smooth_move(current_x, current_y, new_x, new_y)

        except Exception as e:
            print(f"Erreur mouvement: {e}")

    def smooth_move(self, start_x, start_y, end_x, end_y):
        """Mouvement fluide avec interpolation"""
        try:
            # Nombre d'étapes basé sur la distance
            distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
            steps = max(1, min(10, int(distance / 10)))

            for i in range(steps + 1):
                t = i / steps if steps > 0 else 1

                # Interpolation avec courbe d'accélération
                t_smooth = t * t * (3 - 2 * t)  # Courbe smooth

                x = int(start_x + (end_x - start_x) * t_smooth)
                y = int(start_y + (end_y - start_y) * t_smooth)

                win32api.SetCursorPos((x, y))

                if i < steps:
                    time.sleep(0.001)  # Délai minimal

        except Exception as e:
            pass

    def execute_button_action(self):
        """Exécute l'action du bouton configuré"""
        try:
            # Délai avant clic (simulation humaine)
            time.sleep(random.uniform(0.01, 0.05))

            # Exécution de l'action du bouton 1
            if self.button1_action == "Left Click":
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
                time.sleep(0.01)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
            elif self.button1_action == "Right Click":
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0)
                time.sleep(0.01)
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0)
            elif self.button1_action == "Middle Click":
                win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEDOWN, 0, 0)
                time.sleep(0.01)
                win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEUP, 0, 0)

            # Exécution de l'action du bouton 2 (si configurée)
            if self.button2_action != "None":
                time.sleep(0.02)  # Petit délai entre les actions

                if self.button2_action == "Left Click":
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
                    time.sleep(0.01)
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
                elif self.button2_action == "Right Click":
                    win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0)
                    time.sleep(0.01)
                    win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0)
                elif self.button2_action == "Middle Click":
                    win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEDOWN, 0, 0)
                    time.sleep(0.01)
                    win32api.mouse_event(win32con.MOUSEEVENTF_MIDDLEUP, 0, 0)

        except Exception as e:
            print(f"Erreur action bouton: {e}")
    

                

    

    
    def save_config(self):
        """Sauvegarde la configuration complète"""
        config = {
            "target_color": self.target_color,
            "color_name": self.color_name,
            "button1_action": self.button1_action,
            "button2_action": self.button2_action,
            "fov_x": self.fov_x,
            "fov_y": self.fov_y,
            "smooth_x": self.smooth_x,
            "smooth_y": self.smooth_y,
            "speed": self.speed,
            "offset_x": self.offset_x,
            "offset_y": self.offset_y,
            "only_x_axis": self.only_x_axis,
            "stealth_mode": self.stealth_mode
        }

        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            print("Configuration sauvegardée!")
        except Exception as e:
            print(f"Erreur sauvegarde: {e}")

    def load_config(self):
        """Charge la configuration complète"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                self.target_color = tuple(config.get("target_color", (128, 0, 128)))
                self.color_name = config.get("color_name", "Purple")
                self.button1_action = config.get("button1_action", "Left Click")
                self.button2_action = config.get("button2_action", "None")
                self.fov_x = config.get("fov_x", 95)
                self.fov_y = config.get("fov_y", 75)
                self.smooth_x = config.get("smooth_x", 10.9)
                self.smooth_y = config.get("smooth_y", 14)
                self.speed = config.get("speed", 1)
                self.offset_x = config.get("offset_x", 1)
                self.offset_y = config.get("offset_y", 7)
                self.only_x_axis = config.get("only_x_axis", False)
                self.stealth_mode = config.get("stealth_mode", True)

            except Exception as e:
                print(f"Erreur chargement config: {e}")
    
    def run(self):
        """Lance l'application professionnelle"""
        try:
            # Sauvegarde automatique à la fermeture
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Raccourcis clavier
            self.root.bind('<F1>', lambda e: self.toggle_aimbot())
            self.root.bind('<Escape>', lambda e: self.stop_aimbot())

            # Focus pour les raccourcis
            self.root.focus_set()

            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_aimbot()

    def on_closing(self):
        """Gestion de la fermeture avec sauvegarde"""
        self.stop_aimbot()
        self.save_config()  # Sauvegarde automatique
        time.sleep(0.1)
        self.root.destroy()

if __name__ == "__main__":
    # Vérification des dépendances
    try:
        import pyautogui
        import numpy as np
        from PIL import Image, ImageTk
        import win32api
        import win32con
        import win32gui
        import psutil
    except ImportError as e:
        print(f"Dépendance manquante: {e}")
        print("Installez avec: pip install pyautogui numpy pillow pywin32 psutil")
        input("Appuyez sur Entrée pour quitter...")
        exit(1)

    # Configuration pyautogui pour les performances
    pyautogui.FAILSAFE = False
    pyautogui.PAUSE = 0

    print("🎯 88Software Pixel Bot 10.0.0 - Démarrage...")
    print("Raccourcis: F1 = Toggle, Escape = Stop")

    # Lance l'application
    app = AimbotProfessional()
    app.run()
