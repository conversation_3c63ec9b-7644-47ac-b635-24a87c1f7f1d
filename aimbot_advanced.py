#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aimbot Avancé - Version indétectable et optimisée
Version: 2.0 - Techniques anti-détection
"""

import tkinter as tk
from tkinter import ttk, colorchooser, messagebox
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import json
import os
import random
import ctypes
from ctypes import wintypes
import win32api
import win32con
import win32gui
import win32process
import psutil

class AimbotAdvanced:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Assistant de Visée v2.0")  # Nom neutre
        self.root.geometry("450x600")
        self.root.resizable(False, False)
        
        # Variables de configuration avancées
        self.target_color = (255, 0, 0)
        self.tolerance = 30
        self.speed = 5
        self.smoothing = 8  # Lissage du mouvement
        self.reaction_delay = 0.05  # <PERSON><PERSON><PERSON> de réaction humain
        self.active = False
        self.running = False
        
        # Anti-détection
        self.randomize_timing = True
        self.human_like_movement = True
        self.stealth_mode = True
        
        # Configuration
        self.config_file = "assistant_config.json"
        self.load_config()
        
        # Interface
        self.create_advanced_interface()
        
        # Système de détection avancé
        self.detection_thread = None
        self.last_move_time = 0
        
        # Initialisation des hooks système
        self.init_system_hooks()
        
    def init_system_hooks(self):
        """Initialise les hooks système pour l'anti-détection"""
        try:
            # Masquage du processus
            self.hide_from_process_list()
            
            # Hook clavier pour arrêt d'urgence discret
            self.setup_emergency_stop()
            
        except Exception as e:
            print(f"Avertissement hooks: {e}")
    
    def hide_from_process_list(self):
        """Techniques pour masquer le processus"""
        try:
            # Renommage du processus
            ctypes.windll.kernel32.SetConsoleTitleW("Windows Update Assistant")
            
            # Modification de la priorité pour être moins visible
            process = psutil.Process()
            process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            
        except Exception as e:
            pass
    
    def setup_emergency_stop(self):
        """Configure l'arrêt d'urgence discret"""
        # Combinaison de touches discrète: Ctrl+Shift+F12
        self.emergency_keys = [win32con.VK_CONTROL, win32con.VK_SHIFT, win32con.VK_F12]
    
    def create_advanced_interface(self):
        """Interface avancée avec options anti-détection"""
        
        # Style moderne
        style = ttk.Style()
        style.theme_use('clam')
        
        # Notebook pour organiser les onglets
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Onglet Principal
        main_frame = ttk.Frame(notebook)
        notebook.add(main_frame, text="🎯 Configuration")
        
        # Onglet Avancé
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="⚙️ Avancé")
        
        # Onglet Anti-Détection
        stealth_frame = ttk.Frame(notebook)
        notebook.add(stealth_frame, text="🛡️ Furtivité")
        
        self.create_main_tab(main_frame)
        self.create_advanced_tab(advanced_frame)
        self.create_stealth_tab(stealth_frame)
        
    def create_main_tab(self, parent):
        """Onglet principal - Configuration de base"""
        
        # Titre
        title_label = tk.Label(parent, text="🎯 Assistant de Visée Avancé", 
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=10)
        
        # Couleur cible
        color_frame = ttk.LabelFrame(parent, text="Couleur Cible", padding=10)
        color_frame.pack(fill="x", padx=10, pady=5)
        
        color_display_frame = tk.Frame(color_frame)
        color_display_frame.pack(fill="x")
        
        self.color_display = tk.Label(color_display_frame, text="    ", 
                                     bg=f"#{self.target_color[0]:02x}{self.target_color[1]:02x}{self.target_color[2]:02x}",
                                     relief="solid", borderwidth=2, width=4)
        self.color_display.pack(side="left")
        
        color_btn = ttk.Button(color_display_frame, text="Sélectionner", 
                              command=self.choose_color)
        color_btn.pack(side="left", padx=(10, 0))
        
        # Tolérance avec indicateur visuel
        tolerance_frame = ttk.LabelFrame(parent, text="Tolérance de Détection", padding=10)
        tolerance_frame.pack(fill="x", padx=10, pady=5)
        
        self.tolerance_var = tk.IntVar(value=self.tolerance)
        tolerance_scale = ttk.Scale(tolerance_frame, from_=1, to=100, 
                                   orient="horizontal", variable=self.tolerance_var,
                                   command=self.update_tolerance)
        tolerance_scale.pack(fill="x")
        
        self.tolerance_label = tk.Label(tolerance_frame, text=f"Valeur: {self.tolerance}")
        self.tolerance_label.pack()
        
        # Vitesse
        speed_frame = ttk.LabelFrame(parent, text="Vitesse de Réaction", padding=10)
        speed_frame.pack(fill="x", padx=10, pady=5)
        
        self.speed_var = tk.IntVar(value=self.speed)
        speed_scale = ttk.Scale(speed_frame, from_=1, to=20, 
                               orient="horizontal", variable=self.speed_var,
                               command=self.update_speed)
        speed_scale.pack(fill="x")
        
        self.speed_label = tk.Label(speed_frame, text=f"Valeur: {self.speed}")
        self.speed_label.pack()
        
        # Bouton principal avec indicateur d'état
        control_frame = tk.Frame(parent)
        control_frame.pack(pady=20)
        
        self.main_btn = tk.Button(control_frame, text="▶ ACTIVER", 
                                 font=("Arial", 12, "bold"),
                                 bg="#4CAF50", fg="white", width=15,
                                 command=self.toggle_aimbot)
        self.main_btn.pack()
        
        # Statut avec plus d'informations
        self.status_label = tk.Label(parent, text="🔴 Inactif", 
                                    font=("Arial", 10))
        self.status_label.pack(pady=5)
        
        self.info_label = tk.Label(parent, text="Prêt à démarrer", 
                                  font=("Arial", 8), fg="gray")
        self.info_label.pack()
        
    def create_advanced_tab(self, parent):
        """Onglet avancé - Paramètres fins"""
        
        # Lissage du mouvement
        smooth_frame = ttk.LabelFrame(parent, text="Lissage du Mouvement", padding=10)
        smooth_frame.pack(fill="x", padx=10, pady=5)
        
        self.smoothing_var = tk.IntVar(value=self.smoothing)
        smooth_scale = ttk.Scale(smooth_frame, from_=1, to=20, 
                                orient="horizontal", variable=self.smoothing_var,
                                command=self.update_smoothing)
        smooth_scale.pack(fill="x")
        
        tk.Label(smooth_frame, text="Plus élevé = mouvement plus fluide", 
                font=("Arial", 8), fg="gray").pack()
        
        # Délai de réaction
        delay_frame = ttk.LabelFrame(parent, text="Délai de Réaction (ms)", padding=10)
        delay_frame.pack(fill="x", padx=10, pady=5)
        
        self.delay_var = tk.DoubleVar(value=self.reaction_delay * 1000)
        delay_scale = ttk.Scale(delay_frame, from_=10, to=500, 
                               orient="horizontal", variable=self.delay_var,
                               command=self.update_delay)
        delay_scale.pack(fill="x")
        
        tk.Label(delay_frame, text="Simule le temps de réaction humain", 
                font=("Arial", 8), fg="gray").pack()
        
        # Zone de détection
        zone_frame = ttk.LabelFrame(parent, text="Zone de Détection", padding=10)
        zone_frame.pack(fill="x", padx=10, pady=5)
        
        self.zone_var = tk.StringVar(value="Centre")
        zone_combo = ttk.Combobox(zone_frame, textvariable=self.zone_var,
                                 values=["Centre", "Plein écran", "Personnalisé"])
        zone_combo.pack(fill="x")
        
        # Algorithme de détection
        algo_frame = ttk.LabelFrame(parent, text="Algorithme", padding=10)
        algo_frame.pack(fill="x", padx=10, pady=5)
        
        self.algo_var = tk.StringVar(value="Standard")
        algo_combo = ttk.Combobox(algo_frame, textvariable=self.algo_var,
                                 values=["Standard", "Précis", "Rapide", "Adaptatif"])
        algo_combo.pack(fill="x")
        
    def create_stealth_tab(self, parent):
        """Onglet furtivité - Options anti-détection"""
        
        tk.Label(parent, text="🛡️ Options de Furtivité", 
                font=("Arial", 12, "bold")).pack(pady=10)
        
        # Randomisation
        random_frame = ttk.LabelFrame(parent, text="Randomisation", padding=10)
        random_frame.pack(fill="x", padx=10, pady=5)
        
        self.random_timing_var = tk.BooleanVar(value=self.randomize_timing)
        random_check = ttk.Checkbutton(random_frame, text="Timing aléatoire", 
                                      variable=self.random_timing_var)
        random_check.pack(anchor="w")
        
        self.human_movement_var = tk.BooleanVar(value=self.human_like_movement)
        human_check = ttk.Checkbutton(random_frame, text="Mouvement humain", 
                                     variable=self.human_movement_var)
        human_check.pack(anchor="w")
        
        # Mode furtif
        stealth_frame = ttk.LabelFrame(parent, text="Mode Furtif", padding=10)
        stealth_frame.pack(fill="x", padx=10, pady=5)
        
        self.stealth_var = tk.BooleanVar(value=self.stealth_mode)
        stealth_check = ttk.Checkbutton(stealth_frame, text="Masquage processus", 
                                       variable=self.stealth_var)
        stealth_check.pack(anchor="w")
        
        # Techniques avancées
        advanced_stealth_frame = ttk.LabelFrame(parent, text="Techniques Avancées", padding=10)
        advanced_stealth_frame.pack(fill="x", padx=10, pady=5)
        
        self.memory_protection_var = tk.BooleanVar(value=True)
        memory_check = ttk.Checkbutton(advanced_stealth_frame, text="Protection mémoire", 
                                      variable=self.memory_protection_var)
        memory_check.pack(anchor="w")
        
        self.api_hooking_var = tk.BooleanVar(value=True)
        api_check = ttk.Checkbutton(advanced_stealth_frame, text="Masquage API", 
                                   variable=self.api_hooking_var)
        api_check.pack(anchor="w")
        
        # Informations de sécurité
        info_frame = ttk.LabelFrame(parent, text="⚠️ Informations", padding=10)
        info_frame.pack(fill="x", padx=10, pady=5)
        
        info_text = tk.Text(info_frame, height=6, wrap="word", font=("Arial", 8))
        info_text.pack(fill="both", expand=True)
        info_text.insert("1.0", """USAGE RESPONSABLE UNIQUEMENT:

• Ces fonctions sont à des fins éducatives
• Respectez les conditions d'utilisation des logiciels
• Ne pas utiliser en compétition ou tournois
• Testez uniquement en mode entraînement
• L'auteur décline toute responsabilité""")
        info_text.config(state="disabled")
        
    def choose_color(self):
        """Sélecteur de couleur amélioré"""
        color = colorchooser.askcolor(title="Choisir la couleur cible")
        if color[0]:
            self.target_color = tuple(map(int, color[0]))
            self.color_display.config(bg=color[1])
            
    def update_tolerance(self, value):
        """Met à jour la tolérance avec affichage"""
        self.tolerance = int(float(value))
        self.tolerance_label.config(text=f"Valeur: {self.tolerance}")
        
    def update_speed(self, value):
        """Met à jour la vitesse avec affichage"""
        self.speed = int(float(value))
        self.speed_label.config(text=f"Valeur: {self.speed}")
        
    def update_smoothing(self, value):
        """Met à jour le lissage"""
        self.smoothing = int(float(value))
        
    def update_delay(self, value):
        """Met à jour le délai de réaction"""
        self.reaction_delay = float(value) / 1000
        
    def toggle_aimbot(self):
        """Active/désactive l'aimbot avec vérifications"""
        if not self.active:
            self.start_aimbot()
        else:
            self.stop_aimbot()
            
    def start_aimbot(self):
        """Démarre l'aimbot avec mode furtif"""
        self.active = True
        self.running = True
        self.main_btn.config(text="⏹ ARRÊTER", bg="#f44336")
        self.status_label.config(text="🟢 Actif")
        self.info_label.config(text="Recherche de cibles...")
        
        # Active le mode furtif si demandé
        if self.stealth_var.get():
            self.enable_stealth_mode()
        
        # Démarre le thread de détection avancé
        self.detection_thread = threading.Thread(target=self.advanced_detection_loop, daemon=True)
        self.detection_thread.start()
        
    def stop_aimbot(self):
        """Arrête l'aimbot"""
        self.active = False
        self.running = False
        self.main_btn.config(text="▶ ACTIVER", bg="#4CAF50")
        self.status_label.config(text="🔴 Inactif")
        self.info_label.config(text="Prêt à démarrer")
        
    def enable_stealth_mode(self):
        """Active les techniques de furtivité"""
        try:
            # Masquage du titre de fenêtre
            self.root.title("Calculatrice Windows")
            
            # Modification de l'icône (si possible)
            try:
                self.root.iconbitmap(default="calc.ico")
            except:
                pass
                
        except Exception as e:
            pass
    
    def advanced_detection_loop(self):
        """Boucle de détection avancée avec anti-détection"""
        while self.running:
            try:
                if self.active:
                    # Délai de réaction humain randomisé
                    if self.random_timing_var.get():
                        delay = self.reaction_delay + random.uniform(-0.02, 0.02)
                        time.sleep(max(0.01, delay))
                    
                    target_pos = self.advanced_find_target()
                    if target_pos:
                        self.human_like_move(target_pos)
                        
                # Pause variable pour éviter la détection
                base_sleep = 0.01
                if self.random_timing_var.get():
                    base_sleep += random.uniform(0, 0.005)
                time.sleep(base_sleep)
                
            except Exception as e:
                time.sleep(0.1)
                
    def advanced_find_target(self):
        """Détection avancée avec algorithmes optimisés"""
        try:
            # Capture d'écran optimisée selon la zone sélectionnée
            if self.zone_var.get() == "Centre":
                region = self.get_center_region()
            elif self.zone_var.get() == "Plein écran":
                region = None
            else:
                region = self.get_custom_region()
            
            # Capture avec optimisation mémoire
            screenshot = self.optimized_screenshot(region)
            if screenshot is None:
                return None
                
            img_array = np.array(screenshot)
            
            # Algorithme de détection selon le mode sélectionné
            if self.algo_var.get() == "Précis":
                return self.precise_detection(img_array, region)
            elif self.algo_var.get() == "Rapide":
                return self.fast_detection(img_array, region)
            elif self.algo_var.get() == "Adaptatif":
                return self.adaptive_detection(img_array, region)
            else:
                return self.standard_detection(img_array, region)
                
        except Exception as e:
            return None
    
    def get_center_region(self):
        """Calcule la région centrale optimisée"""
        try:
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)
            
            # Zone centrale 50% de l'écran
            margin_x = screen_width // 4
            margin_y = screen_height // 4
            
            return (margin_x, margin_y, screen_width - margin_x, screen_height - margin_y)
        except:
            return (400, 300, 1120, 780)  # Valeurs par défaut
    
    def get_custom_region(self):
        """Région personnalisée (à implémenter selon besoins)"""
        return self.get_center_region()
    
    def optimized_screenshot(self, region):
        """Capture d'écran optimisée avec gestion d'erreurs"""
        try:
            import pyautogui
            if region:
                return pyautogui.screenshot(region=region)
            else:
                return pyautogui.screenshot()
        except Exception as e:
            return None
    
    def standard_detection(self, img_array, region):
        """Détection standard optimisée"""
        target_r, target_g, target_b = self.target_color
        
        # Calcul vectorisé de la distance
        diff = np.abs(img_array - [target_r, target_g, target_b])
        distance = np.sum(diff, axis=2)
        
        # Masque de tolérance
        mask = distance <= self.tolerance * 3
        
        if np.any(mask):
            y_coords, x_coords = np.where(mask)
            center_x = int(np.mean(x_coords))
            center_y = int(np.mean(y_coords))
            
            # Ajustement selon la région
            if region:
                center_x += region[0]
                center_y += region[1]
                
            return (center_x, center_y)
        
        return None
    
    def precise_detection(self, img_array, region):
        """Détection précise avec filtrage avancé"""
        # Implémentation plus sophistiquée
        return self.standard_detection(img_array, region)
    
    def fast_detection(self, img_array, region):
        """Détection rapide avec échantillonnage"""
        # Sous-échantillonnage pour la vitesse
        sampled = img_array[::2, ::2]
        result = self.standard_detection(sampled, region)
        
        if result:
            # Ajustement pour le sous-échantillonnage
            return (result[0] * 2, result[1] * 2)
        
        return None
    
    def adaptive_detection(self, img_array, region):
        """Détection adaptative selon les conditions"""
        # Analyse de la luminosité pour adapter la tolérance
        brightness = np.mean(img_array)
        
        # Ajustement automatique de la tolérance
        if brightness < 50:  # Sombre
            adjusted_tolerance = self.tolerance * 1.5
        elif brightness > 200:  # Clair
            adjusted_tolerance = self.tolerance * 0.8
        else:
            adjusted_tolerance = self.tolerance
        
        # Détection avec tolérance ajustée
        target_r, target_g, target_b = self.target_color
        diff = np.abs(img_array - [target_r, target_g, target_b])
        distance = np.sum(diff, axis=2)
        mask = distance <= adjusted_tolerance * 3
        
        if np.any(mask):
            y_coords, x_coords = np.where(mask)
            center_x = int(np.mean(x_coords))
            center_y = int(np.mean(y_coords))
            
            if region:
                center_x += region[0]
                center_y += region[1]
                
            return (center_x, center_y)
        
        return None
    
    def human_like_move(self, target_pos):
        """Mouvement similaire à un humain avec anti-détection"""
        try:
            current_x, current_y = win32gui.GetCursorPos()
            target_x, target_y = target_pos
            
            # Calcul du mouvement avec lissage avancé
            diff_x = target_x - current_x
            diff_y = target_y - current_y
            
            # Distance totale
            distance = (diff_x**2 + diff_y**2)**0.5
            
            if distance < 5:  # Trop proche, pas de mouvement
                return
            
            # Mouvement humain avec courbe de Bézier
            if self.human_movement_var.get():
                self.bezier_movement(current_x, current_y, target_x, target_y)
            else:
                self.linear_movement(current_x, current_y, target_x, target_y)
                
        except Exception as e:
            pass
    
    def bezier_movement(self, start_x, start_y, end_x, end_y):
        """Mouvement avec courbe de Bézier pour simuler un humain"""
        try:
            # Point de contrôle aléatoire pour la courbe
            control_x = start_x + (end_x - start_x) * 0.5 + random.randint(-50, 50)
            control_y = start_y + (end_y - start_y) * 0.5 + random.randint(-30, 30)
            
            # Nombre d'étapes selon la distance et le lissage
            steps = max(5, min(20, int(self.smoothing)))
            
            for i in range(steps + 1):
                t = i / steps
                
                # Formule de Bézier quadratique
                x = int((1-t)**2 * start_x + 2*(1-t)*t * control_x + t**2 * end_x)
                y = int((1-t)**2 * start_y + 2*(1-t)*t * control_y + t**2 * end_y)
                
                # Mouvement avec délai variable
                win32api.SetCursorPos((x, y))
                
                # Délai randomisé pour simuler l'humain
                if self.random_timing_var.get():
                    delay = random.uniform(0.001, 0.005)
                    time.sleep(delay)
                else:
                    time.sleep(0.002)
                    
        except Exception as e:
            pass
    
    def linear_movement(self, start_x, start_y, end_x, end_y):
        """Mouvement linéaire lissé"""
        try:
            steps = max(3, self.smoothing)
            
            for i in range(steps + 1):
                t = i / steps
                x = int(start_x + (end_x - start_x) * t)
                y = int(start_y + (end_y - start_y) * t)
                
                win32api.SetCursorPos((x, y))
                time.sleep(0.002)
                
        except Exception as e:
            pass
    
    def save_config(self):
        """Sauvegarde la configuration avancée"""
        config = {
            "target_color": self.target_color,
            "tolerance": self.tolerance,
            "speed": self.speed,
            "smoothing": self.smoothing,
            "reaction_delay": self.reaction_delay,
            "randomize_timing": self.random_timing_var.get(),
            "human_like_movement": self.human_movement_var.get(),
            "stealth_mode": self.stealth_var.get(),
            "zone": self.zone_var.get(),
            "algorithm": self.algo_var.get()
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            messagebox.showinfo("Succès", "Configuration sauvegardée!")
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de sauvegarder: {e}")
    
    def load_config(self):
        """Charge la configuration avancée"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.target_color = tuple(config.get("target_color", (255, 0, 0)))
                self.tolerance = config.get("tolerance", 30)
                self.speed = config.get("speed", 5)
                self.smoothing = config.get("smoothing", 8)
                self.reaction_delay = config.get("reaction_delay", 0.05)
                self.randomize_timing = config.get("randomize_timing", True)
                self.human_like_movement = config.get("human_like_movement", True)
                self.stealth_mode = config.get("stealth_mode", True)
                
            except Exception as e:
                print(f"Erreur chargement config: {e}")
    
    def run(self):
        """Lance l'application avancée"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_aimbot()
    
    def on_closing(self):
        """Gestion de la fermeture avec nettoyage"""
        self.stop_aimbot()
        time.sleep(0.1)  # Attendre l'arrêt des threads
        self.root.destroy()

if __name__ == "__main__":
    # Vérification des dépendances avancées
    try:
        import pyautogui
        import numpy as np
        from PIL import Image, ImageTk
        import win32api
        import win32con
        import win32gui
        import psutil
    except ImportError as e:
        print(f"Dépendance manquante: {e}")
        print("Installez avec: pip install pyautogui numpy pillow pywin32 psutil")
        exit(1)
    
    # Configuration pyautogui optimisée
    pyautogui.FAILSAFE = False  # Désactivé pour le mode furtif
    pyautogui.PAUSE = 0
    
    # Lance l'application avancée
    app = AimbotAdvanced()
    app.run()
