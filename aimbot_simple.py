#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aimbot Simple - Logiciel de visée automatique par couleur
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, colorchooser, messagebox
import pyautogui
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import json
import os

class AimbotSimple:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Aimbot Simple v1.0")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Variables de configuration
        self.target_color = (255, 0, 0)  # Rouge par défaut
        self.tolerance = 30
        self.speed = 5
        self.active = False
        self.running = False
        
        # Configuration par défaut
        self.config_file = "aimbot_config.json"
        self.load_config()
        
        # Interface
        self.create_interface()
        
        # Thread de détection
        self.detection_thread = None
        
    def create_interface(self):
        """Crée l'interface utilisateur simple"""
        
        # Titre
        title_label = tk.Label(self.root, text="🎯 Aimbot Simple", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Frame couleur cible
        color_frame = tk.Frame(self.root)
        color_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(color_frame, text="Couleur cible:", font=("Arial", 10)).pack(anchor="w")
        
        color_display_frame = tk.Frame(color_frame)
        color_display_frame.pack(fill="x", pady=5)
        
        self.color_display = tk.Label(color_display_frame, text="    ", 
                                     bg=f"#{self.target_color[0]:02x}{self.target_color[1]:02x}{self.target_color[2]:02x}",
                                     relief="solid", borderwidth=2)
        self.color_display.pack(side="left")
        
        color_btn = tk.Button(color_display_frame, text="Changer couleur", 
                             command=self.choose_color)
        color_btn.pack(side="left", padx=(10, 0))
        
        # Tolérance
        tolerance_frame = tk.Frame(self.root)
        tolerance_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(tolerance_frame, text="Tolérance de couleur:", font=("Arial", 10)).pack(anchor="w")
        self.tolerance_var = tk.IntVar(value=self.tolerance)
        tolerance_scale = tk.Scale(tolerance_frame, from_=1, to=100, 
                                  orient="horizontal", variable=self.tolerance_var,
                                  command=self.update_tolerance)
        tolerance_scale.pack(fill="x")
        
        # Vitesse
        speed_frame = tk.Frame(self.root)
        speed_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(speed_frame, text="Vitesse de visée:", font=("Arial", 10)).pack(anchor="w")
        self.speed_var = tk.IntVar(value=self.speed)
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, 
                              orient="horizontal", variable=self.speed_var,
                              command=self.update_speed)
        speed_scale.pack(fill="x")
        
        # Bouton principal
        self.main_btn = tk.Button(self.root, text="▶ ACTIVER", 
                                 font=("Arial", 14, "bold"),
                                 bg="#4CAF50", fg="white",
                                 command=self.toggle_aimbot)
        self.main_btn.pack(pady=20)
        
        # Statut
        self.status_label = tk.Label(self.root, text="🔴 Inactif", 
                                    font=("Arial", 12))
        self.status_label.pack(pady=5)
        
        # Instructions
        instructions = tk.Text(self.root, height=8, width=45, wrap="word")
        instructions.pack(pady=10, padx=20)
        instructions.insert("1.0", """📋 INSTRUCTIONS:

1. Choisissez la couleur à viser
2. Ajustez la tolérance (plus élevé = détection plus large)
3. Réglez la vitesse de visée
4. Cliquez sur ACTIVER
5. Appuyez sur 'Q' pour arrêter d'urgence

⚠️ ATTENTION: Utilisez de manière responsable et légale uniquement.""")
        instructions.config(state="disabled")
        
        # Boutons de sauvegarde
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=10)
        
        save_btn = tk.Button(btn_frame, text="💾 Sauvegarder", command=self.save_config)
        save_btn.pack(side="left", padx=5)
        
        load_btn = tk.Button(btn_frame, text="📁 Charger", command=self.load_config)
        load_btn.pack(side="left", padx=5)
        
    def choose_color(self):
        """Ouvre le sélecteur de couleur"""
        color = colorchooser.askcolor(title="Choisir la couleur cible")
        if color[0]:  # Si une couleur a été sélectionnée
            self.target_color = tuple(map(int, color[0]))
            self.color_display.config(bg=color[1])
            
    def update_tolerance(self, value):
        """Met à jour la tolérance"""
        self.tolerance = int(value)
        
    def update_speed(self, value):
        """Met à jour la vitesse"""
        self.speed = int(value)
        
    def toggle_aimbot(self):
        """Active/désactive l'aimbot"""
        if not self.active:
            self.start_aimbot()
        else:
            self.stop_aimbot()
            
    def start_aimbot(self):
        """Démarre l'aimbot"""
        self.active = True
        self.running = True
        self.main_btn.config(text="⏹ ARRÊTER", bg="#f44336")
        self.status_label.config(text="🟢 Actif - Recherche de cibles...")
        
        # Démarre le thread de détection
        self.detection_thread = threading.Thread(target=self.detection_loop, daemon=True)
        self.detection_thread.start()
        
        # Bind pour arrêt d'urgence
        self.root.bind('<KeyPress-q>', lambda e: self.stop_aimbot())
        self.root.focus_set()
        
    def stop_aimbot(self):
        """Arrête l'aimbot"""
        self.active = False
        self.running = False
        self.main_btn.config(text="▶ ACTIVER", bg="#4CAF50")
        self.status_label.config(text="🔴 Inactif")
        
    def detection_loop(self):
        """Boucle principale de détection"""
        while self.running:
            try:
                if self.active:
                    target_pos = self.find_target()
                    if target_pos:
                        self.move_to_target(target_pos)
                        
                time.sleep(0.01)  # Petite pause pour éviter la surcharge CPU
                
            except Exception as e:
                print(f"Erreur dans la détection: {e}")
                time.sleep(0.1)
                
    def find_target(self):
        """Trouve la position de la couleur cible à l'écran"""
        try:
            # Capture d'écran de la zone centrale (plus rapide)
            screen_width, screen_height = pyautogui.size()
            region = (screen_width//4, screen_height//4, 
                     screen_width//2, screen_height//2)
            
            screenshot = pyautogui.screenshot(region=region)
            img_array = np.array(screenshot)
            
            # Recherche de la couleur cible
            target_r, target_g, target_b = self.target_color
            
            # Calcul de la distance de couleur
            diff = np.abs(img_array - [target_r, target_g, target_b])
            distance = np.sum(diff, axis=2)
            
            # Trouve les pixels dans la tolérance
            mask = distance <= self.tolerance * 3
            
            if np.any(mask):
                # Trouve le centre de masse des pixels correspondants
                y_coords, x_coords = np.where(mask)
                center_x = int(np.mean(x_coords)) + region[0]
                center_y = int(np.mean(y_coords)) + region[1]
                
                return (center_x, center_y)
                
        except Exception as e:
            print(f"Erreur capture: {e}")
            
        return None
        
    def move_to_target(self, target_pos):
        """Déplace la souris vers la cible"""
        try:
            current_x, current_y = pyautogui.position()
            target_x, target_y = target_pos
            
            # Calcul du mouvement avec lissage
            diff_x = target_x - current_x
            diff_y = target_y - current_y
            
            # Applique la vitesse
            move_x = diff_x * (self.speed / 100)
            move_y = diff_y * (self.speed / 100)
            
            # Déplace la souris
            new_x = current_x + move_x
            new_y = current_y + move_y
            
            pyautogui.moveTo(new_x, new_y, duration=0.01)
            
        except Exception as e:
            print(f"Erreur mouvement: {e}")
            
    def save_config(self):
        """Sauvegarde la configuration"""
        config = {
            "target_color": self.target_color,
            "tolerance": self.tolerance,
            "speed": self.speed
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            messagebox.showinfo("Succès", "Configuration sauvegardée!")
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de sauvegarder: {e}")
            
    def load_config(self):
        """Charge la configuration"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.target_color = tuple(config.get("target_color", (255, 0, 0)))
                self.tolerance = config.get("tolerance", 30)
                self.speed = config.get("speed", 5)
                
                # Met à jour l'interface si elle existe
                if hasattr(self, 'tolerance_var'):
                    self.tolerance_var.set(self.tolerance)
                    self.speed_var.set(self.speed)
                    self.color_display.config(bg=f"#{self.target_color[0]:02x}{self.target_color[1]:02x}{self.target_color[2]:02x}")
                    
            except Exception as e:
                print(f"Erreur chargement config: {e}")
                
    def run(self):
        """Lance l'application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_aimbot()
            
    def on_closing(self):
        """Gestion de la fermeture"""
        self.stop_aimbot()
        self.root.destroy()

if __name__ == "__main__":
    # Vérification des dépendances
    try:
        import pyautogui
        import numpy as np
        from PIL import Image, ImageTk
    except ImportError as e:
        print(f"Dépendance manquante: {e}")
        print("Installez avec: pip install pyautogui numpy pillow")
        exit(1)
    
    # Configuration pyautogui
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.01
    
    # Lance l'application
    app = AimbotSimple()
    app.run()
