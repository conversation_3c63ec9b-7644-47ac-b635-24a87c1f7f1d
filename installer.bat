@echo off
echo ========================================
echo    Installation Aimbot Simple v1.0
echo ========================================
echo.

echo Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Telechargez Python depuis: https://www.python.org/downloads/
    echo Assurez-vous de cocher "Add Python to PATH" lors de l'installation
    pause
    exit /b 1
)

echo Python detecte!
echo.

echo Installation des dependances...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERREUR lors de l'installation des dependances
    echo Essayez manuellement: pip install pyautogui numpy pillow
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Installation terminee avec succes!
echo ========================================
echo.
echo Pour lancer le logiciel, double-cliquez sur: lancer.bat
echo Ou tapez: python aimbot_simple.py
echo.
pause
