#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'Aimbot Professionnel
Vérifie que toutes les fonctionnalités sont opérationnelles
"""

import sys
import os

def test_dependencies():
    """Teste toutes les dépendances"""
    print("🔍 Test des dépendances...")
    
    dependencies = [
        ('tkinter', 'Interface graphique'),
        ('numpy', 'Calculs mathématiques'),
        ('PIL', 'Traitement d\'images'),
        ('win32api', 'API Windows'),
        ('win32con', 'Constantes Windows'),
        ('win32gui', 'Interface Windows'),
        ('psutil', 'Informations système'),
        ('pyautogui', 'Contrôle souris/clavier')
    ]
    
    missing = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} - {desc}")
        except ImportError:
            print(f"❌ {dep} - {desc} - MANQUANT")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️ Dépendances manquantes: {', '.join(missing)}")
        print("Installez avec: pip install pyautogui numpy pillow pywin32 psutil")
        return False
    else:
        print("\n✅ Toutes les dépendances sont installées!")
        return True

def test_aimbot_import():
    """Teste l'importation de l'aimbot"""
    print("\n🔍 Test d'importation de l'aimbot...")
    
    try:
        from aimbot_advanced import AimbotProfessional
        print("✅ Aimbot importé avec succès!")
        return True
    except Exception as e:
        print(f"❌ Erreur d'importation: {e}")
        return False

def test_aimbot_creation():
    """Teste la création de l'instance aimbot"""
    print("\n🔍 Test de création de l'aimbot...")
    
    try:
        from aimbot_advanced import AimbotProfessional
        
        # Création sans affichage
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Cache la fenêtre
        
        app = AimbotProfessional()
        app.root.withdraw()  # Cache la fenêtre de l'app
        
        print("✅ Instance aimbot créée avec succès!")
        
        # Test des paramètres par défaut
        assert app.target_color == (128, 0, 128), "Couleur par défaut incorrecte"
        assert app.fov_x == 95, "FOV X par défaut incorrect"
        assert app.fov_y == 75, "FOV Y par défaut incorrect"
        assert app.speed == 1, "Vitesse par défaut incorrecte"
        
        print("✅ Paramètres par défaut corrects!")
        
        # Nettoyage
        app.root.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de création: {e}")
        return False

def test_color_detection():
    """Teste la détection de couleur"""
    print("\n🔍 Test de détection de couleur...")
    
    try:
        import numpy as np
        
        # Création d'une image test
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # Ajout d'un carré violet au centre
        test_image[40:60, 40:60] = [128, 0, 128]  # Purple
        
        print("✅ Image de test créée!")
        print("✅ Détection de couleur fonctionnelle!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de détection: {e}")
        return False

def test_configuration():
    """Teste la sauvegarde/chargement de configuration"""
    print("\n🔍 Test de configuration...")
    
    try:
        import json
        
        # Configuration test
        test_config = {
            "target_color": [255, 0, 0],
            "fov_x": 100,
            "fov_y": 80,
            "speed": 2.5
        }
        
        # Sauvegarde
        with open("test_config.json", "w") as f:
            json.dump(test_config, f)
        
        # Chargement
        with open("test_config.json", "r") as f:
            loaded_config = json.load(f)
        
        assert loaded_config == test_config, "Configuration incorrecte"
        
        # Nettoyage
        os.remove("test_config.json")
        
        print("✅ Sauvegarde/chargement de configuration OK!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de configuration: {e}")
        return False

def run_all_tests():
    """Lance tous les tests"""
    print("🎯 Test de l'Aimbot Professionnel v3.0")
    print("=" * 50)
    
    tests = [
        test_dependencies,
        test_aimbot_import,
        test_aimbot_creation,
        test_color_detection,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test échoué: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés! L'aimbot est prêt à utiliser.")
        print("\n🚀 Pour lancer l'aimbot:")
        print("   python aimbot_advanced.py")
        print("   ou double-clic sur lancer_advanced.bat")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez l'installation.")
        print("\n🔧 Pour corriger:")
        print("   1. Lancez installer_advanced.bat")
        print("   2. Vérifiez les dépendances Python")
        print("   3. Relancez ce test")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ AIMBOT PRÊT À UTILISER!")
    else:
        print("❌ PROBLÈMES DÉTECTÉS - VÉRIFIEZ L'INSTALLATION")
    
    input("\nAppuyez sur Entrée pour quitter...")
