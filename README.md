# 🎯 Aimbot Simple v1.0

Logiciel de visée automatique par détection de couleur - **Version la plus simple possible**

## 🚀 Installation Ultra-Rapide

1. **Double-cliquez sur `installer.bat`** - Installe automatiquement tout
2. **Double-cliquez sur `lancer.bat`** - Lance le logiciel

C'est tout ! 

## 📋 Utilisation

### Interface Simple
- **Couleur cible** : C<PERSON>z sur "Changer couleur" pour sélectionner
- **Tolérance** : Plus élevé = détection plus large (recommandé: 20-40)
- **Vitesse** : Rapidité de la visée (recommandé: 5-10)
- **Bouton ACTIVER** : Lance/arrête l'aimbot

### Contrôles
- **Activer** : Clic sur le bouton vert
- **Arrêt d'urgence** : Appuyez sur `Q` ou cliquez sur ARRÊTER
- **Sauvegarde** : Bouton "💾 Sauvegarder" pour garder vos réglages

## ⚙️ Fonctionnement

1. **Choisissez votre couleur cible** (ex: rouge pour les ennemis)
2. **Ajustez la tolérance** selon l'éclairage du jeu
3. **Réglez la vitesse** pour un mouvement naturel
4. **Activez** et le logiciel vise automatiquement

## 🎮 Conseils d'Utilisation

### Réglages Recommandés
- **FPS Games** : Tolérance 25-35, Vitesse 7-12
- **Jeux colorés** : Tolérance 15-25, Vitesse 5-8
- **Conditions sombres** : Tolérance 35-50, Vitesse 6-10

### Optimisation
- Utilisez des couleurs **distinctives** (rouge vif, vert fluo)
- Évitez les couleurs communes dans l'environnement
- Testez d'abord en mode entraînement

## 🔧 Dépannage

### Le logiciel ne se lance pas
```bash
# Installation manuelle
pip install pyautogui numpy pillow
python aimbot_simple.py
```

### Détection imprécise
- Augmentez la tolérance
- Choisissez une couleur plus distinctive
- Vérifiez l'éclairage du jeu

### Mouvement trop rapide/lent
- Ajustez le curseur "Vitesse"
- Valeurs basses = mouvement plus lisse
- Valeurs hautes = réaction plus rapide

## ⚠️ Avertissements

- **Usage responsable uniquement**
- **Respectez les règles des jeux**
- **Ne pas utiliser en compétition**
- **Testez d'abord hors ligne**

## 📁 Fichiers

- `aimbot_simple.py` - Programme principal
- `installer.bat` - Installation automatique
- `lancer.bat` - Lancement rapide
- `requirements.txt` - Dépendances Python
- `aimbot_config.json` - Configuration sauvegardée (créé automatiquement)

## 🆘 Support

En cas de problème :
1. Vérifiez que Python est installé
2. Relancez `installer.bat`
3. Vérifiez les permissions d'administrateur
4. Désactivez temporairement l'antivirus

---

**Version** : 1.0 - La plus simple possible  
**Compatibilité** : Windows 10/11  
**Prérequis** : Python 3.7+
